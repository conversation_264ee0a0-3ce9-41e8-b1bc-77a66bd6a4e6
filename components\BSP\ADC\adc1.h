/**
 ****************************************************************************************************
 * @file        adc1.h
 * <AUTHOR>
 * @version     V1.0
 * @date        2023-08-26
 * @brief       ADC驱动代码
 * @license     Copyright (c) 2020-2032, 广州市星翼电子科技有限公司
 ****************************************************************************************************
 * @attention
 *
 * 实验平台:正点原子 ESP32-S3 开发板
 * 在线视频:www.yuanzige.com
 * 技术论坛:www.openedv.com
 * 公司网址:www.alientek.com
 * 购买地址:openedv.taobao.com
 * 
 ****************************************************************************************************
 */

#ifndef __ADC_H_
#define __ADC_H_

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "driver/adc.h"
#include "esp_log.h"


#define ADC_ADCX_CHY   ADC1_CHANNEL_5
#define ADC_WIDTH      ADC_WIDTH_BIT_12
#define ADC_ATTEN      ADC_ATTEN_DB_11

/* Function declarations */
void adc_init(void);                                            /* Initialize ADC */
uint32_t adc_get_result_average(adc1_channel_t channel, uint32_t times);   /* Get ADC result with average filtering */
float adc_get_voltage(adc1_channel_t channel, uint32_t times);   /* Get ADC voltage value */

#endif
