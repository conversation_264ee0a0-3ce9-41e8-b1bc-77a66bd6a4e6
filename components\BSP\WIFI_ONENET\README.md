# WiFi OneNET 模块使用说明

## 概述

本模块实现了ESP32连接WiFi网络并与中国移动OneNET云平台进行MQTT通信的功能。支持设备数据上报、云端命令接收、自动重连等特性。

## 主要功能

- ✅ WiFi网络连接管理（自动连接、重连、状态监控）
- ✅ OneNET MQTT客户端（连接、订阅、发布、断线重连）
- ✅ ADC数据自动上报（定时上报，保留1位小数）
- ✅ 云端命令接收和处理（属性设置、自定义命令）
- ✅ 裸机版本实现（不使用FreeRTOS任务）
- ✅ 便捷的一键初始化接口

## 文件结构

```
WIFI_ONENET/
├── wifi_onenet.h      # 头文件（接口定义、配置参数）
├── wifi_onenet.c      # 实现文件（所有功能实现）
└── README.md          # 使用说明文档
```

## 快速开始

### 1. 配置参数

在 `wifi_onenet.h` 中修改以下配置参数：

```c
/* WiFi配置 */
#define WIFI_SSID               "your_wifi_ssid"        // 您的WiFi名称
#define WIFI_PASSWORD           "your_wifi_password"    // 您的WiFi密码

/* OneNET配置 */
#define ONENET_PRODUCT_ID       "your_product_id"       // OneNET产品ID
#define ONENET_DEVICE_ID        "your_device_id"        // OneNET设备ID
#define ONENET_DEVICE_TOKEN     "your_device_token"     // OneNET设备Token
```

### 2. 获取OneNET参数

1. 登录 [OneNET平台](https://open.iot.10086.cn/)
2. 创建产品，获取 `产品ID`
3. 在产品下创建设备，获取 `设备ID`
4. 在设备页面生成 `Token`，复制完整的Token字符串

### 3. 裸机版本使用示例（推荐）

```c
#include "wifi_onenet.h"

// WiFi状态回调函数
void wifi_status_callback(bool connected) {
    if (connected) {
        ESP_LOGI("APP", "WiFi连接成功，启动OneNET服务");
        onenet_mqtt_start();
        onenet_set_auto_report_interval(5000);  // 每5秒自动上报
    } else {
        ESP_LOGI("APP", "WiFi断开，停止OneNET服务");
        onenet_set_auto_report_interval(0);     // 禁用自动上报
        onenet_mqtt_stop();
    }
}

// OneNET命令回调函数
void onenet_cmd_callback(const char* topic, const char* data, int data_len) {
    ESP_LOGI("APP", "收到OneNET命令: %.*s", data_len, data);
    // 在这里处理云端下发的命令
}

void app_main(void) {
    // 初始化NVS
    nvs_flash_init();

    // 初始化WiFi和OneNET
    wifi_onenet_init(wifi_status_callback, onenet_cmd_callback);

    // 连接WiFi（非阻塞）
    wifi_connect();

    // 主循环（重要：必须定期调用onenet_process）
    float adc_voltage = 0.0f;
    int64_t last_manual_report = 0;

    while (1) {
        onenet_process();  // 处理WiFi状态、MQTT连接、自动上报等

        // 每5秒手动上报一次ADC数据
        int64_t current_time = esp_timer_get_time() / 1000;
        if ((current_time - last_manual_report) >= 5000) {
            if (onenet_mqtt_get_state() == MQTT_STATE_CONNECTED) {
                // 模拟读取ADC值
                adc_voltage += 0.1f;
                if (adc_voltage > 3.3f) adc_voltage = 0.0f;

                // 使用便捷函数上报ADC数据
                int msg_id = onenet_report_adc(adc_voltage);
                if (msg_id != -1) {
                    ESP_LOGI("APP", "ADC上报成功: %.1fV", adc_voltage);
                }
            }
            last_manual_report = current_time;
        }

        vTaskDelay(pdMS_TO_TICKS(100));  // 延迟100ms
    }
}
```

### 4. 一键初始化示例（裸机版本）

```c
void app_main(void) {
    // 初始化NVS
    nvs_flash_init();

    // 一键初始化（包含默认回调函数）
    wifi_onenet_example_init();

    // 主循环（重要：必须定期调用onenet_process）
    while (1) {
        onenet_process();  // 处理WiFi状态、MQTT连接、自动上报等
        vTaskDelay(pdMS_TO_TICKS(100));  // 延迟100ms
    }
}
```

## API 接口说明

### WiFi 功能

| 函数 | 说明 |
|------|------|
| `wifi_init()` | 初始化WiFi功能 |
| `wifi_connect()` | 连接WiFi网络 |
| `wifi_disconnect()` | 断开WiFi连接 |
| `wifi_is_connected()` | 获取WiFi连接状态 |

### OneNET MQTT 功能

| 函数 | 说明 |
|------|------|
| `onenet_mqtt_init()` | 初始化OneNET MQTT客户端 |
| `onenet_mqtt_start()` | 启动MQTT连接 |
| `onenet_mqtt_stop()` | 停止MQTT连接 |
| `onenet_mqtt_get_state()` | 获取MQTT连接状态 |

### 数据上报功能

| 函数 | 说明 |
|------|------|
| `onenet_report_property()` | 上报单个属性 |
| `onenet_report_properties()` | 上报多个属性 |
| `onenet_report_adc()` | 上报ADC数据（便捷函数，保留1位小数） |
| `onenet_set_auto_report_interval()` | 设置自动上报间隔（裸机版本） |
| `onenet_process()` | 处理函数（需在主循环中调用） |

### 便捷功能

| 函数 | 说明 |
|------|------|
| `wifi_onenet_init()` | 一键初始化WiFi和OneNET |
| `wifi_onenet_status_info()` | 打印状态信息（调试用） |
| `wifi_onenet_example_init()` | 完整示例初始化（裸机版本） |
| `wifi_get_state()` | 获取详细WiFi状态 |

## 数据格式说明

### 上报数据格式

#### ADC数据格式（标准OneNET格式）
```json
{
  "id": "123",
  "params": {
    "ADC": {
      "value": 3.1
    }
  }
}
```

#### 多属性数据格式（标准OneNET格式）
```json
{
  "id": "123",
  "params": {
    "ADC": {
      "value": 3.1
    },
    "counter": {
      "value": 42
    },
    "timestamp": {
      "value": 1640995200000
    },
    "device_status": {
      "value": "online"
    }
  }
}
```

#### 使用onenet_fill_buf函数构造数据包
```c
// 类似OneNet_FillBuf的使用方式
char buf[1024];
int len = onenet_fill_buf(buf, sizeof(buf));
if (len > 0) {
    ESP_LOGI("APP", "构造的数据包: %s", buf);
    ESP_LOGI("APP", "数据包长度: %d", len);
}
```

### 命令接收格式

OneNET平台下发的命令格式：

```json
{
  "id": "123",
  "version": "1.0",
  "params": {
    "led": {
      "value": "on"
    }
  }
}
```

## 注意事项

1. **配置参数**：使用前必须正确配置WiFi和OneNET参数
2. **初始化顺序**：必须先调用 `nvs_flash_init()` 初始化NVS存储
3. **网络依赖**：MQTT功能依赖WiFi连接，建议在WiFi连接成功后再启动MQTT
4. **裸机版本**：必须在主循环中定期调用 `onenet_process()` 函数，建议间隔不超过100ms
5. **非阻塞设计**：WiFi连接和MQTT连接都是非阻塞的，状态通过回调函数通知
6. **内存优化**：裸机版本不使用FreeRTOS任务，内存占用更少
7. **线程安全**：所有API都是线程安全的，可以在不同任务中调用

## 故障排除

### 常见问题

1. **WiFi连接失败**
   - 检查SSID和密码是否正确
   - 确认WiFi信号强度
   - 查看串口日志中的错误信息

2. **MQTT连接失败**
   - 检查OneNET参数是否正确
   - 确认设备Token是否有效
   - 检查网络连接是否正常

3. **数据上报失败**
   - 确认MQTT已连接
   - 检查JSON格式是否正确
   - 查看OneNET平台是否收到数据

### 调试方法

```c
// 打印详细状态信息
wifi_onenet_status_info();

// 检查连接状态
if (wifi_is_connected()) {
    ESP_LOGI("DEBUG", "WiFi已连接");
}

if (onenet_mqtt_get_state() == MQTT_STATE_CONNECTED) {
    ESP_LOGI("DEBUG", "MQTT已连接");
}
```

## 更新日志

- **v1.0** (2024-08-18)
  - 初始版本发布
  - 支持WiFi连接和OneNET MQTT通信
  - 支持自动数据上报和命令接收
  - 完整的错误处理和日志记录
