{"version": "1.2", "project_name": "24_touch", "project_version": "1", "project_path": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project", "idf_path": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1", "build_dir": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build", "config_file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/sdkconfig", "config_defaults": "", "bootloader_elf": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/bootloader/bootloader.elf", "app_elf": "24_touch.elf", "app_bin": "24_touch.bin", "build_type": "flash_app", "git_revision": "v5.4.1-dirty", "target": "esp32s3", "rev": "", "min_rev": "0", "max_rev": "99", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32s3-elf-", "c_compiler": "D:/ESP-IDF/Espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe", "config_environment": {"COMPONENT_KCONFIGS": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bt/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ana_cmpr/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_cam/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_dac/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gptimer/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_isp/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_jpeg/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ledc/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_parlio/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_pcnt/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdm/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_touch_sens/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_tsens/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_uart/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_ota/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_server/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_ringbuf/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ieee802154/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_sec_provider/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/openthread/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ulp/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/vfs/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling/Kconfig;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/Kconfig;C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader/Kconfig.projbuild;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format/Kconfig.projbuild;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/Kconfig.projbuild;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py/Kconfig.projbuild;D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "xtensa"], "build_components": ["BSP", "app_trace", "app_update", "bootloader", "bootloader_support", "bt", "cmock", "console", "cxx", "driver", "efuse", "esp-tls", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_coex", "esp_common", "esp_driver_ana_cmpr", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_security", "esp_system", "esp_timer", "esp_vfs_console", "esp_wifi", "espcoredump", "esptool_py", "fatfs", "freertos", "guider", "hal", "heap", "http_parser", "idf_test", "ieee802154", "json", "log", "lvgl", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "partition_table", "perfmon", "protobuf-c", "protocomm", "pthread", "rt", "sdmmc", "soc", "spi_flash", "spiffs", "tcp_transport", "touch_element", "ulp", "unity", "usb", "vfs", "wear_levelling", "wifi_provisioning", "wpa_supplicant", "xtensa", ""], "build_component_paths": ["C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_update", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bt", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cmock", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cxx", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ana_cmpr", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_cam", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_dac", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gptimer", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_isp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_jpeg", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ledc", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_parlio", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_pcnt", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ppa", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdio", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdm", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdmmc", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdspi", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_touch_sens", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_tsens", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_uart", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_ota", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_server", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif_stack", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_ringbuf", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_vfs_console", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/http_parser", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/idf_test", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ieee802154", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/json", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/main", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_sec_provider", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/openthread", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/perfmon", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protobuf-c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/rt", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/touch_element", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ulp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/vfs", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa", ""], "build_component_info": {"BSP": {"alias": "idf::BSP", "target": "___idf_BSP", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP", "type": "LIBRARY", "lib": "__idf_BSP", "reqs": ["driver", "esp_lcd", "esp_common", "log", "lvgl", "esp_wifi", "esp_netif", "esp_event", "mqtt", "nvs_flash"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/BSP/libBSP.a", "sources": ["C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/ADC/adc1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/IIC/iic.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/LED/led.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/RGBLCD/ltdc.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/TOUCH/gt9xxx.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/TOUCH/touch.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/XL9555/xl9555.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/lv_driver/lv_port_disp.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/lv_driver/lv_port_fs.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/lv_driver/lv_port_indev.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP/WIFI_ONENET/wifi_onenet.c"], "include_dirs": ["ADC", "IIC", "LED", "RGBLCD", "TOUCH", "XL9555", "lv_driver", "WIFI_ONENET"]}, "app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/app_trace/libapp_trace.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace/app_trace.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace/app_trace_util.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace/host_file_io.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/app_update/libapp_update.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_update/esp_ota_ops.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_common_loader.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_clock_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_mem.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_random.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_efuse.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/flash_encrypt.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/secure_boot.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_random_esp32s3.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/bootloader_utility.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/flash_partitions.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/esp_image_format.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/idf/bootloader_sha.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support/src/esp32s3/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bt", "type": "CONFIG_ONLY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/cmock/libcmock.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/console/libconsole.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/commands.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/esp_console_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/split_argv.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/linenoise/linenoise.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/esp_console_repl_chip.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_cmd.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_date.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_dbl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_dstr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_end.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_file.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_hashtable.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_int.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_lit.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_rem.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_rex.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_str.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/arg_utils.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console/argtable3/argtable3.c"], "include_dirs": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/cxx/libcxx.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cxx/cxx_exception_stubs.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cxx/cxx_guards.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cxx/cxx_init.cpp"], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/driver/libdriver.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/deprecated/adc_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/deprecated/adc_dma_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/deprecated/timer_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/i2c/i2c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/deprecated/i2s_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/deprecated/mcpwm_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/deprecated/pcnt_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/deprecated/rmt_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/deprecated/sigma_delta_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/deprecated/rtc_temperature_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/touch_sensor/touch_sensor_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/touch_sensor/esp32s3/touch_sensor.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver/twai/twai.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/efuse/libefuse.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/esp32s3/esp_efuse_table.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/esp32s3/esp_efuse_fields.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/esp32s3/esp_efuse_rtc_calib.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/esp32s3/esp_efuse_utility.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/src/esp_efuse_api.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/src/esp_efuse_fields.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/src/esp_efuse_utility.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp_tls.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp_tls_error_capture.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp_tls_platform_port.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/adc_oneshot.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/adc_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/adc_cali.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/adc_cali_curve_fitting.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/adc_continuous.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/adc_monitor.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/gdma/adc_dma.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/adc_filter.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/esp32s3/curve_fitting_coefficients.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc/deprecated/esp32s3/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex/esp32s3/esp_coex_adapter.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex/src/coexist_debug_diagram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_common/libesp_common.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_cam/esp_cam_ctlr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_dac", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio/src/gpio.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio/src/rtc_io.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio/src/dedic_gpio.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio/src/gpio_pin_glitch_filter.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gptimer/src/gptimer.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c/i2c_master.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c/i2c_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s/i2s_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s/i2s_std.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s/i2s_pdm.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s/i2s_tdm.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s/i2s_platform.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm", "type": "LIBRARY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_mcpwm/libesp_driver_mcpwm.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm/src/mcpwm_cap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm/src/mcpwm_cmpr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm/src/mcpwm_com.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm/src/mcpwm_fault.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm/src/mcpwm_gen.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm/src/mcpwm_oper.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm/src/mcpwm_sync.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm/src/mcpwm_timer.c"], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_pcnt", "type": "LIBRARY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_pcnt/libesp_driver_pcnt.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_pcnt/src/pulse_cnt.c"], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt/src/rmt_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt/src/rmt_encoder.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt/src/rmt_rx.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdmmc", "type": "LIBRARY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_sdmmc/libesp_driver_sdmmc.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdmmc/src/sdmmc_transaction.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdmmc/src/sdmmc_host.c"], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdspi/src/sdspi_crc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdspi/src/sdspi_host.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi/src/gpspi/spi_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi/src/gpspi/spi_master.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi/src/gpspi/spi_slave.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi/src/gpspi/spi_dma.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi/src/gpspi/spi_slave_hd.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_touch_sens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_tsens", "type": "LIBRARY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_tsens/libesp_driver_tsens.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_tsens/src/temperature_sensor.c"], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_uart/src/uart.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag", "type": "LIBRARY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_connection_monitor.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_vfs.c"], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth", "type": "LIBRARY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_eth/libesp_eth.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth/src/esp_eth.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth/src/phy/esp_eth_phy_802_3.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth/src/esp_eth_netif_glue.c"], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_event/libesp_event.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/default_event_loop.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/esp_event.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub/src/gdbstub.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub/src/gdbstub_transport.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub/src/packet.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub/src/port/xtensa/gdbstub_xtensa.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub/src/port/xtensa/gdbstub-entry.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub/src/port/xtensa/xt_debugexception.S"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid/src/esp_hidd.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid/src/esp_hidh.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid/src/esp_hid_common.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client/esp_http_client.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client/lib/http_auth.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client/lib/http_header.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server/src/httpd_main.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server/src/httpd_parse.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server/src/httpd_sess.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server/src/httpd_txrx.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server/src/httpd_uri.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server/src/httpd_ws.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/cpu.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/esp_memory_utils.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/cpu_region_protect.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/esp_clk.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/clk_ctrl_os.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/hw_random.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/intr_alloc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/mac_addr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/periph_ctrl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/revision.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/rtc_module.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/sleep_modem.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/sleep_modes.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/sleep_console.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/sleep_usb.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/sleep_gpio.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/sleep_event.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/regi2c_ctrl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/esp_gpio_reserve.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/sar_periph_ctrl_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/io_mux.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/esp_clk_tree.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp_clk_tree_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/esp_dma_utils.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/gdma_link.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/spi_share_hw_ctrl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/spi_bus_lock.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/clk_utils.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/adc_share_hw_ctrl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/gdma.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/deprecated/gdma_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/esp_async_memcpy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/dma/async_memcpy_gdma.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/systimer.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/mspi_timing_tuning.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/mspi_timing_by_mspi_delay.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/sleep_wake_stub.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/esp_clock_output.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/rtc_clk.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/rtc_clk_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/rtc_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/rtc_sleep.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/rtc_time.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/chip_info.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/sar_periph_ctrl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/mspi_timing_config.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp32s3/esp_memprot.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/port/esp_memprot_conv.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support/lowpower/port/esp32s3/sleep_cpu.c"], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/src/esp_lcd_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/src/esp_lcd_panel_io.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/src/esp_lcd_panel_st7789.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/src/esp_lcd_panel_ops.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/spi/esp_lcd_panel_io_spi.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/i80/esp_lcd_panel_io_i80.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd/rgb/esp_lcd_panel_rgb.c"], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl/src/esp_local_ctrl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm/esp_mmu_map.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm/port/esp32s3/ext_mem_layout.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm/esp_cache.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/esp_netif_handlers.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/esp_netif_objects.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/esp_netif_defaults.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/lwip/esp_netif_lwip.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/lwip/esp_netif_sntp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/lwip/netif/wlanif.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/lwip/netif/ethernetif.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/partition.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/src/phy_override.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/src/lib_printf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/src/phy_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/src/phy_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/esp32s3/phy_init_data.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm/pm_locks.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm/pm_trace.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram", "type": "LIBRARY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_psram/libesp_psram.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram/esp_psram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram/mmu_psram_flash.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram/esp32s3/esp_psram_impl_octal.c"], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_sys.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_print.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_crc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_uart.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_spiflash.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_efuse.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_gpio.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_longjmp.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_systimer.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_wdt.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S"], "include_dirs": ["include", "esp32s3/include", "esp32s3/include/esp32s3", "esp32s3"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security", "type": "LIBRARY", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_security/libesp_security.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security/src/init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security/src/esp_hmac.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security/src/esp_ds.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security/src/esp_crypto_lock.c"], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_system/libesp_system.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/esp_err.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/crosscore_int.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/esp_ipc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/freertos_hooks.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/int_wdt.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/panic.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/esp_system.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/startup.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/startup_funcs.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/system_time.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/stack_check.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/ubsan.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/xt_wdt.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/task_wdt/task_wdt.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/cpu_start.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/panic_handler.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/esp_system_chip.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/image_process.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/brownout.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/esp_ipc_isr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/esp_ipc_isr_port.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/esp_ipc_isr_handler.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/esp_ipc_isr_routines.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/panic_arch.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/panic_handler_asm.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/expression_with_stack.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/expression_with_stack_asm.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/debug_helpers.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/debug_helpers_asm.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/debug_stubs.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/arch/xtensa/trax.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc/esp32s3/highint_hdl.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc/esp32s3/clk.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc/esp32s3/reset_reason.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc/esp32s3/system_internal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc/esp32s3/cache_err_int.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system/port/soc/esp32s3/apb_backup_dma.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/src/esp_timer.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/src/esp_timer_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/src/ets_timer_legacy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/src/system_time.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/src/esp_timer_impl_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer/src/esp_timer_impl_systimer.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/src/lib_printf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/src/mesh_event.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/src/smartconfig.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/src/wifi_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/src/wifi_default.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/src/wifi_netif.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/src/wifi_default_ap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/esp32s3/esp_adapter.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/src/core_dump_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/src/core_dump_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/src/core_dump_flash.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/src/core_dump_uart.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/src/core_dump_elf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/src/core_dump_binary.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/src/core_dump_sha.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/src/core_dump_crc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump/src/port/xtensa/core_dump_port.c"], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/fatfs/libfatfs.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/diskio/diskio.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/diskio/diskio_rawflash.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/diskio/diskio_wl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/src/ff.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/src/ffunicode.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/port/freertos/ffsystem.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/diskio/diskio_sdmmc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/vfs/vfs_fat.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/vfs/vfs_fat_sdmmc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/freertos/libfreertos.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/heap_idf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/app_startup.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/port_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/port_systick.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/list.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/queue.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/tasks.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/timers.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/event_groups.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/port.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/portasm.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/FreeRTOS-Kernel/portable/xtensa/xtensa_overlay_os_hook.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/freertos_compatibility.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/idf_additions_event_groups.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "guider": {"alias": "idf::guider", "target": "___idf_guider", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider", "type": "LIBRARY", "lib": "__idf_guider", "reqs": ["lvgl", "esp_common", "log"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/guider/libguider.a", "sources": ["C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/custom.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/events_init.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/gui_guider.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/setup_scr_screen.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/widgets_init.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/guider_fonts/lv_font_SourceHanSerifSC_Regular_18.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/guider_fonts/lv_font_montserratMedium_12.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/guider_fonts/lv_font_montserratMedium_16.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/guider_fonts/lv_font_montserratMedium_20.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/guider_fonts/lv_font_montserratMedium_33.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider/guider_fonts/lv_font_montserratMedium_41.c"], "include_dirs": [".", "guider_fonts", "guider_customer_fonts", "images"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/hal/libhal.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/hal_utils.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/mpu_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/efuse_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32s3/efuse_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/mmu_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/cache_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/color_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/spi_flash_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/spi_flash_hal_iram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/spi_flash_encrypt_hal_iram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32s3/clk_tree_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/systimer_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/uart_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/uart_hal_iram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/gpio_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/rtc_io_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/timer_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/ledc_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/ledc_hal_iram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/i2c_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/i2c_hal_iram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/rmt_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/pcnt_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/mcpwm_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/twai_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/twai_hal_iram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/gdma_hal_top.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/gdma_hal_ahb_v1.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/i2s_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/sdm_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/sdmmc_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/adc_hal_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/adc_oneshot_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/adc_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/lcd_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/mpi_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/sha_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/aes_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/brownout_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/spi_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/spi_hal_iram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/spi_slave_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/spi_slave_hal_iram.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/spi_slave_hd_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/spi_flash_hal_gpspi.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/hmac_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/ds_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/usb_serial_jtag_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/usb_dwc_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/usb_wrap_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32s3/touch_sensor_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/touch_sensor_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/xt_wdt_hal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal/esp32s3/rtc_cntl_hal.c"], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/heap/libheap.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap/heap_caps_base.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap/heap_caps.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap/heap_caps_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap/multi_heap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap/tlsf/tlsf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap/port/memory_layout_utils.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap/port/esp32s3/memory_layout.c"], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/json/libjson.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/json/cJSON/cJSON.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/log/liblog.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/os/log_timestamp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/log_timestamp_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/os/log_lock.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/os/log_write.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/buffer/log_buffers.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/util.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/log_level/log_level.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/log_level/tag_log_level/tag_log_level.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/log_level/tag_log_level/linked_list/log_linked_list.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log/src/log_level/tag_log_level/cache/log_binary_heap.c"], "include_dirs": ["include"]}, "lvgl": {"alias": "idf::lvgl", "target": "___idf_lvgl", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl", "type": "LIBRARY", "lib": "__idf_lvgl", "reqs": ["esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/lvgl/liblvgl.a", "sources": ["C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_disp.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_event.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_group.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_indev.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_indev_scroll.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_obj.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_obj_class.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_obj_draw.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_obj_pos.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_obj_scroll.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_obj_style.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_obj_style_gen.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_obj_tree.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_refr.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/core/lv_theme.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/arm2d/lv_gpu_arm2d.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw_arc.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw_img.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw_label.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw_layer.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw_line.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw_mask.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw_rect.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw_transform.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_draw_triangle.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_img_buf.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_img_cache.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/lv_img_decoder.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/pxp/lv_draw_pxp.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/pxp/lv_draw_pxp_blend.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/pxp/lv_gpu_nxp_pxp_osa.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite_arc.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite_blend.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite_line.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/vglite/lv_draw_vglite_rect.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/vglite/lv_vglite_buf.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/nxp/vglite/lv_vglite_utils.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/renesas/lv_gpu_d2_draw_label.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/renesas/lv_gpu_d2_ra6m3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_arc.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_bg.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_composite.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_img.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_label.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_layer.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_line.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_mask.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_polygon.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_rect.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_stack_blur.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_texture_cache.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sdl/lv_draw_sdl_utils.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/stm32_dma2d/lv_gpu_stm32_dma2d.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_arc.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_blend.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_dither.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_gradient.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_img.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_layer.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_letter.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_line.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_polygon.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_rect.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/sw/lv_draw_sw_transform.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/draw/swm341_dma2d/lv_gpu_swm341_dma2d.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/layouts/flex/lv_flex.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/layouts/grid/lv_grid.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/bmp/lv_bmp.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/ffmpeg/lv_ffmpeg.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/freetype/lv_freetype.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/fsdrv/lv_fs_fatfs.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/fsdrv/lv_fs_posix.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/fsdrv/lv_fs_stdio.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/fsdrv/lv_fs_win32.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/gif/gifdec.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/gif/lv_gif.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/png/lodepng.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/png/lv_png.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/qrcode/lv_qrcode.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/qrcode/qrcodegen.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/rlottie/lv_rlottie.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/sjpg/lv_sjpg.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/libs/sjpg/tjpgd.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/lv_extra.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/others/fragment/lv_fragment.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/others/fragment/lv_fragment_manager.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/others/gridnav/lv_gridnav.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/others/ime/lv_ime_pinyin.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/others/imgfont/lv_imgfont.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/others/monkey/lv_monkey.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/others/msg/lv_msg.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/others/snapshot/lv_snapshot.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/themes/basic/lv_theme_basic.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/themes/default/lv_theme_default.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/themes/mono/lv_theme_mono.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/animimg/lv_animimg.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/calendar/lv_calendar.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/calendar/lv_calendar_header_arrow.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/calendar/lv_calendar_header_dropdown.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/chart/lv_chart.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/colorwheel/lv_colorwheel.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/imgbtn/lv_imgbtn.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/keyboard/lv_keyboard.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/led/lv_led.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/list/lv_list.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/menu/lv_menu.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/meter/lv_meter.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/msgbox/lv_msgbox.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/span/lv_span.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/spinbox/lv_spinbox.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/spinner/lv_spinner.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/tabview/lv_tabview.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/tileview/lv_tileview.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/extra/widgets/win/lv_win.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_dejavu_16_persian_hebrew.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_fmt_txt.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_loader.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_10.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_12.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_12_subpx.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_14.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_16.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_18.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_20.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_22.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_24.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_26.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_28.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_28_compressed.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_30.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_32.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_34.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_36.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_38.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_40.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_42.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_44.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_46.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_48.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_montserrat_8.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_simsun_16_cjk.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_unscii_16.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/font/lv_font_unscii_8.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/hal/lv_hal_disp.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/hal/lv_hal_indev.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/hal/lv_hal_tick.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_anim.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_anim_timeline.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_area.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_async.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_bidi.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_color.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_fs.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_gc.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_ll.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_log.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_lru.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_math.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_mem.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_printf.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_style.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_style_gen.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_templ.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_timer.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_tlsf.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_txt.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_txt_ap.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/misc/lv_utils.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_arc.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_bar.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_btn.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_btnmatrix.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_canvas.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_checkbox.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_dropdown.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_img.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_label.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_line.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_objx_templ.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_roller.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_slider.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_switch.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_table.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src/widgets/lv_textarea.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/anim/lv_example_anim_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/anim/lv_example_anim_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/anim/lv_example_anim_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/anim/lv_example_anim_timeline_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/animimg001.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/animimg002.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/animimg003.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/emoji/img_emoji_F617.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/img_caret_down.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/img_cogwheel_alpha16.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/img_cogwheel_argb.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/img_cogwheel_chroma_keyed.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/img_cogwheel_indexed16.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/img_cogwheel_rgb.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/img_hand.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/img_skew_strip.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/img_star.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/imgbtn_left.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/imgbtn_mid.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/assets/imgbtn_right.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/event/lv_example_event_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/event/lv_example_event_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/event/lv_example_event_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/event/lv_example_event_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/get_started/lv_example_get_started_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/get_started/lv_example_get_started_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/get_started/lv_example_get_started_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/flex/lv_example_flex_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/flex/lv_example_flex_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/flex/lv_example_flex_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/flex/lv_example_flex_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/flex/lv_example_flex_5.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/flex/lv_example_flex_6.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/grid/lv_example_grid_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/grid/lv_example_grid_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/grid/lv_example_grid_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/grid/lv_example_grid_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/grid/lv_example_grid_5.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/layouts/grid/lv_example_grid_6.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/bmp/lv_example_bmp_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/ffmpeg/lv_example_ffmpeg_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/ffmpeg/lv_example_ffmpeg_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/freetype/lv_example_freetype_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/gif/img_bulb_gif.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/gif/lv_example_gif_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/png/img_wink_png.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/png/lv_example_png_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/qrcode/lv_example_qrcode_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/rlottie/lv_example_rlottie_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/rlottie/lv_example_rlottie_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/rlottie/lv_example_rlottie_approve.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/libs/sjpg/lv_example_sjpg_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/fragment/lv_example_fragment_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/fragment/lv_example_fragment_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/gridnav/lv_example_gridnav_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/gridnav/lv_example_gridnav_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/gridnav/lv_example_gridnav_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/gridnav/lv_example_gridnav_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/ime/lv_example_ime_pinyin_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/ime/lv_example_ime_pinyin_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/imgfont/lv_example_imgfont_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/monkey/lv_example_monkey_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/monkey/lv_example_monkey_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/monkey/lv_example_monkey_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/msg/lv_example_msg_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/msg/lv_example_msg_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/msg/lv_example_msg_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/others/snapshot/lv_example_snapshot_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/porting/lv_port_disp_template.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/porting/lv_port_fs_template.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/porting/lv_port_indev_template.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/scroll/lv_example_scroll_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/scroll/lv_example_scroll_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/scroll/lv_example_scroll_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/scroll/lv_example_scroll_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/scroll/lv_example_scroll_5.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/scroll/lv_example_scroll_6.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_10.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_11.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_12.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_13.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_14.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_15.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_5.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_6.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_7.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_8.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/styles/lv_example_style_9.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/animimg/lv_example_animimg_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/arc/lv_example_arc_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/arc/lv_example_arc_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/bar/lv_example_bar_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/bar/lv_example_bar_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/bar/lv_example_bar_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/bar/lv_example_bar_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/bar/lv_example_bar_5.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/bar/lv_example_bar_6.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/btn/lv_example_btn_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/btn/lv_example_btn_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/btn/lv_example_btn_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/btnmatrix/lv_example_btnmatrix_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/calendar/lv_example_calendar_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/canvas/lv_example_canvas_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/canvas/lv_example_canvas_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/chart/lv_example_chart_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/chart/lv_example_chart_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/chart/lv_example_chart_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/chart/lv_example_chart_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/chart/lv_example_chart_5.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/chart/lv_example_chart_6.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/chart/lv_example_chart_7.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/chart/lv_example_chart_8.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/chart/lv_example_chart_9.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/checkbox/lv_example_checkbox_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/checkbox/lv_example_checkbox_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/colorwheel/lv_example_colorwheel_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/dropdown/lv_example_dropdown_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/dropdown/lv_example_dropdown_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/dropdown/lv_example_dropdown_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/img/lv_example_img_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/img/lv_example_img_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/img/lv_example_img_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/img/lv_example_img_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/imgbtn/lv_example_imgbtn_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/keyboard/lv_example_keyboard_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/label/lv_example_label_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/label/lv_example_label_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/label/lv_example_label_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/label/lv_example_label_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/label/lv_example_label_5.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/led/lv_example_led_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/line/lv_example_line_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/list/lv_example_list_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/list/lv_example_list_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/menu/lv_example_menu_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/menu/lv_example_menu_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/menu/lv_example_menu_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/menu/lv_example_menu_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/menu/lv_example_menu_5.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/meter/lv_example_meter_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/meter/lv_example_meter_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/meter/lv_example_meter_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/meter/lv_example_meter_4.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/msgbox/lv_example_msgbox_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/obj/lv_example_obj_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/obj/lv_example_obj_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/roller/lv_example_roller_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/roller/lv_example_roller_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/roller/lv_example_roller_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/slider/lv_example_slider_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/slider/lv_example_slider_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/slider/lv_example_slider_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/span/lv_example_span_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/spinbox/lv_example_spinbox_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/spinner/lv_example_spinner_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/switch/lv_example_switch_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/table/lv_example_table_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/table/lv_example_table_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/tabview/lv_example_tabview_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/tabview/lv_example_tabview_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/textarea/lv_example_textarea_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/textarea/lv_example_textarea_2.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/textarea/lv_example_textarea_3.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/tileview/lv_example_tileview_1.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples/widgets/win/lv_example_win_1.c"], "include_dirs": ["C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/../", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/demos"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/lwip/liblwip.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/apps/sntp/sntp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/api/api_lib.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/api/api_msg.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/api/err.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/api/if_api.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/api/netbuf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/api/netdb.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/api/netifapi.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/api/sockets.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/api/tcpip.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/apps/sntp/sntp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/def.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/dns.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/inet_chksum.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ip.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/mem.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/memp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/netif.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/pbuf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/raw.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/stats.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/sys.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/tcp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/tcp_in.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/tcp_out.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/timeouts.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/udp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv4/autoip.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv4/dhcp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv4/etharp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv4/icmp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv4/igmp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv4/ip4.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv6/dhcp6.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv6/ethip6.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv6/icmp6.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv6/inet6.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv6/ip6.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv6/mld6.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/core/ipv6/nd6.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ethernet.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/bridgeif.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/bridgeif_fdb.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/slipif.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/auth.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/ccp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/chap-md5.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/chap-new.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/chap_ms.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/demand.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/eap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/ecp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/eui64.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/fsm.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/ipcp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/lcp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/magic.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/mppe.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/multilink.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/ppp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/pppapi.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/pppoe.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/pppos.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/upap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/utils.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/vj.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/hooks/tcp_isn_default.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/hooks/lwip_default_hooks.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/debug/lwip_debug.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/sockets_ext.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/freertos/sys_arch.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/acd_dhcp_check.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/port/esp32xx/vfs_lwip.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/apps/ping/esp_ping.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/apps/ping/ping.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/apps/ping/ping_sock.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": ["guider", "BSP", "lvgl", "nvs_flash"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/main/libmain.a", "sources": ["C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/main/main.c"], "include_dirs": ["."]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/mqtt/libmqtt.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/mqtt_client.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/newlib/libnewlib.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/abort.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/assert.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/heap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/flockfile.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/locks.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/poll.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/pthread.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/random.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/getentropy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/reent_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/newlib_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/syscalls.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/termios.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/stdatomic.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/time.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/sysconf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/realpath.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/scandir.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/port/xtensa/stdatomic_s32c1i.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_api.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_cxx_api.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_item_hash_list.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_page.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_pagemanager.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_storage.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_handle_simple.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_handle_locked.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_partition.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_partition_lookup.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_partition_manager.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_types.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_platform.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_bootloader.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash/src/nvs_encrypted_partition.cpp"], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/perfmon", "type": "LIBRARY", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/perfmon/libperfmon.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/perfmon/xtensa_perfmon_access.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/perfmon/xtensa_perfmon_apis.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/perfmon/xtensa_perfmon_masks.c"], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/protocomm/libprotocomm.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/src/common/protocomm.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/proto-c/constants.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/proto-c/sec0.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/proto-c/sec1.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/proto-c/sec2.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/proto-c/session.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/src/transports/protocomm_console.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/src/transports/protocomm_httpd.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/src/security/security0.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/src/security/security1.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/src/security/security2.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/src/crypto/srp6a/esp_srp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/pthread/libpthread.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread/pthread.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread/pthread_cond_var.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread/pthread_local_storage.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread/pthread_rwlock.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/rt", "type": "LIBRARY", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/rt/librt.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/rt/FreeRTOS_POSIX_mqueue.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/rt/FreeRTOS_POSIX_utils.c"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc/sdmmc_cmd.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc/sdmmc_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc/sdmmc_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc/sdmmc_io.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc/sdmmc_mmc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc/sdmmc_sd.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/soc/libsoc.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/lldesc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/dport_access_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/interrupts.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/gpio_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/uart_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/adc_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/dedic_gpio_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/gdma_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/spi_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/ledc_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/pcnt_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/rmt_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/sdm_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/i2s_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/i2c_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/temperature_sensor_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/timer_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/lcd_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/mcpwm_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/mpi_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/sdmmc_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/touch_sensor_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/twai_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/wdt_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/usb_dwc_periph.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc/esp32s3/rtc_io_periph.c"], "include_dirs": ["include", "esp32s3", "esp32s3/include", "esp32s3/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/flash_brownout_hook.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/esp32s3/spi_flash_oct_flash_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_hpm_enable.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_chip_drivers.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_chip_generic.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_chip_issi.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_chip_mxic.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_chip_gd.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_chip_winbond.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_chip_boya.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_chip_mxic_opi.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_chip_th.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/memspi_host_driver.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/cache_utils.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/flash_mmap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/flash_ops.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_wrap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/esp_flash_api.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/esp_flash_spi_init.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_os_func_app.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/spiffs/libspiffs.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/spiffs_api.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/spiffs/src/spiffs_cache.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/spiffs/src/spiffs_check.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/spiffs/src/spiffs_gc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/spiffs/src/spiffs_hydrogen.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/spiffs/src/spiffs_nucleus.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/transport.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/transport_ssl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/transport_internal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/transport_socks_proxy.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/touch_element", "type": "LIBRARY", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/touch_element/libtouch_element.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/touch_element/touch_element.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/touch_element/touch_button.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/touch_element/touch_slider.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/touch_element/touch_matrix.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/unity/libunity.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity/unity/src/unity.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity/unity_compat.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity/unity_runner.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity/unity_utils_freertos.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity/unity_utils_cache.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity/unity_utils_memory.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity/unity_port_esp32.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb", "type": "LIBRARY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/usb/libusb.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb/hcd_dwc.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb/enum.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb/hub.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb/usb_helpers.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb/usb_host.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb/usb_private.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb/usbh.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb/usb_phy.c"], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/vfs/libvfs.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/vfs/vfs.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/vfs/vfs_eventfd.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/vfs/vfs_semihost.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/vfs/nullfs.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling/Partition.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling/SPI_Flash.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling/WL_Ext_Perf.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling/WL_Ext_Safe.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling/WL_Flash.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling/crc32.cpp", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/src/wifi_config.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/src/wifi_scan.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/src/wifi_ctrl.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/src/manager.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/src/handlers.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/src/scheme_console.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning/src/scheme_softap.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/port/os_xtensa.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/port/eloop.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/ap/ap_config.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/ap/ieee802_1x.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/ap/wpa_auth.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/ap/sta_info.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/ap/ieee802_11.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/ap/comeback_token.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/common/sae.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/common/dragonfly.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/common/wpa_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/utils/bitfield.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/aes-siv.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/sha256-kdf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/ccmp.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/aes-gcm.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/crypto_ops.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/dh_group5.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/dh_groups.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/ms_funcs.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/sha256-prf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/sha1-prf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/sha384-prf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/md4-internal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/sha1-tprf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/common/ieee802_11_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/chap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_peap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_tls.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/mschapv2.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_fast.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/rsn_supp/wpa.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/utils/base64.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/utils/common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/utils/ext_password.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/utils/uuid.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/utils/wpabuf.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/utils/wpa_debug.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/utils/json.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/wps/wps.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/wps/wps_attr_build.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/wps/wps_attr_parse.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/wps/wps_attr_process.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/wps/wps_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/wps/wps_dev_attr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/wps/wps_enrollee.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/common/sae_pk.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/esp_hostap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/rc4.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/des-internal.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/aes-wrap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/aes-unwrap.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/esp-idf/xtensa/libxtensa.a", "sources": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/eri.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/xt_trax.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/xtensa_context.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/xtensa_intr_asm.S", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/xtensa_intr.c", "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa/xtensa_vectors.S"], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include", "touch_sensor/esp32s3/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32s3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_security", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32s3", "dma/include", "ldo/include", "debug_probe/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "rgb/include"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "spi_flash", "partition_table", "bootloader_support", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3/include", "esp32s3/include/esp32s3", "esp32s3"]}, "esp_security": {"alias": "idf::esp_security", "target": "___idf_esp_security", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_security", "lib": "__idf_esp_security", "reqs": [], "priv_reqs": ["efuse", "esp_hw_support", "esp_system", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/xtensa"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/xtensa/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/xtensa/include", "FreeRTOS-Kernel/portable/xtensa/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32s3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "tlsf"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32s3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/perfmon", "lib": "__idf_perfmon", "reqs": ["xtensa"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "rt": {"alias": "idf::rt", "target": "___idf_rt", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/rt", "lib": "__idf_rt", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32s3", "esp32s3/include", "esp32s3/register"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/touch_element", "lib": "__idf_touch_element", "reqs": ["driver"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "D:/ESP-IDF/Espressif/frameworks/esp-idf-v5.4.1/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["esp32s3/include", "include", "deprecated_include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/main", "lib": "__idf_main", "reqs": ["guider", "BSP", "lvgl", "nvs_flash"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "BSP": {"alias": "idf::BSP", "target": "___idf_BSP", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/BSP", "lib": "__idf_BSP", "reqs": ["driver", "esp_lcd", "esp_common", "log", "lvgl", "esp_wifi", "esp_netif", "esp_event", "mqtt", "nvs_flash"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["ADC", "IIC", "LED", "RGBLCD", "TOUCH", "XL9555", "lv_driver", "WIFI_ONENET"]}, "guider": {"alias": "idf::guider", "target": "___idf_guider", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/guider", "lib": "__idf_guider", "reqs": ["lvgl", "esp_common", "log"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": [".", "guider_fonts", "guider_customer_fonts", "images"]}, "lvgl": {"alias": "idf::lvgl", "target": "___idf_lvgl", "prefix": "idf", "dir": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl", "lib": "__idf_lvgl", "reqs": ["esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/src", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/../", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/examples", "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/components/lvgl/demos"]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/gdbinit/symbols", "02_prefix_map": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/gdbinit/prefix_map", "03_py_extensions": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/gdbinit/py_extensions", "04_connect": "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32s3-builtin.cfg"}