/**
 ****************************************************************************************************
 * @file        adc1.c
 * <AUTHOR>
 * @version     V1.0
 * @date        2023-08-26
 * @brief       ADC驱动代码
 * @license     Copyright (c) 2020-2032, 广州市星翼电子科技有限公司
 ****************************************************************************************************
 * @attention
 *
 * 实验平台:正点原子 ESP32-S3 开发板
 * 在线视频:www.yuanzige.com
 * 技术论坛:www.openedv.com
 * 公司网址:www.alientek.com
 * 购买地址:openedv.taobao.com
 * 
 ****************************************************************************************************
 */

#include "adc1.h"

/**
 * @brief       Initialize ADC (Legacy API compatible)
 * @param       None
 * @retval      None
 */
void adc_init(void)
{
    /* Configure ADC1 width and attenuation */
    adc1_config_width(ADC_WIDTH);
    adc1_config_channel_atten(ADC_ADCX_CHY, ADC_ATTEN);
}

/**
 * @brief       Get ADC result with average filtering (Legacy API compatible)
 * @param       channel: ADC1 channel
 * @param       times: Number of samples for averaging
 * @retval      ADC conversion result (raw value)
 */
uint32_t adc_get_result_average(adc1_channel_t channel, uint32_t times)
{
    uint32_t temp_val = 0;

    for (uint32_t i = 0; i < times; i++) {
        temp_val += adc1_get_raw(channel);
        vTaskDelay(pdMS_TO_TICKS(5));  /* Delay 5ms */
    }

    return temp_val / times;  /* Return average value */
}

/**
 * @brief       Get ADC voltage value (Simple calculation)
 * @param       channel: ADC1 channel
 * @param       times: Number of samples for averaging
 * @retval      Voltage value in volts
 */
float adc_get_voltage(adc1_channel_t channel, uint32_t times)
{
    uint32_t adc_raw = adc_get_result_average(channel, times);

    /* Simple calculation: 3.3V reference, 12-bit ADC (4095 max) */
    return (float)adc_raw * (3.3f / 4095.0f);
}
