/**
 * @file lv_port_disp_templ.c
 *
 */

/*Copy this file as "lv_port_disp.c" and set this value to "1" to enable content*/
#if 1

/*********************
 *      INCLUDES
 *********************/
#include "lv_port_disp.h"
#include "../RGBLCD/ltdc.h"
#include <stdbool.h>
#include <string.h>
#include "esp_heap_caps.h"

/*********************
 *      DEFINES
 *********************/
#ifndef MY_DISP_HOR_RES
    #warning Please define or replace the macro MY_DISP_HOR_RES with the actual screen width, default value 320 is used for now.
    #define MY_DISP_HOR_RES    800
#endif

#ifndef MY_DISP_VER_RES
    #warning Please define or replace the macro MY_DISP_HOR_RES with the actual screen height, default value 240 is used for now.
    #define MY_DISP_VER_RES    480
#endif

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void disp_init(void);

static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p);
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//        const lv_area_t * fill_area, lv_color_t color);

/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

void lv_port_disp_init(void)
{
    /*-------------------------
     * Initialize your display
     * -----------------------*/
    disp_init();

    /*-----------------------------
     * Create a buffer for drawing
     *----------------------------*/

    /**
     * LVGL requires a buffer where it internally draws the widgets.
     * Later this buffer will passed to your display driver's `flush_cb` to copy its content to your display.
     * The buffer has to be greater than 1 display row
     *
     * There are 3 buffering configurations:
     * 1. Create ONE buffer:
     *      LVGL will draw the display's content here and writes it to your display
     *
     * 2. Create TWO buffer:
     *      LVGL will draw the display's content to a buffer and writes it your display.
     *      You should use DMA to write the buffer's content to the display.
     *      It will enable LVGL to draw the next part of the screen to the other buffer while
     *      the data is being sent form the first buffer. It makes rendering and flushing parallel.
     *
     * 3. Double buffering
     *      Set 2 screens sized buffers and set disp_drv.full_refresh = 1.
     *      This way LVGL will always provide the whole rendered screen in `flush_cb`
     *      and you only need to change the frame buffer's address.
     */

    /* 使用全局双缓冲配置 - 全屏大小的缓冲区提供最佳性能 */
    static lv_disp_draw_buf_t draw_buf_dsc_3;
    /* 使用动态分配避免静态内存不足 */
    static lv_color_t *buf_3_1;
    static lv_color_t *buf_3_2;

    /* 在PSRAM中分配全屏大小的缓冲区 */
    size_t buffer_size = MY_DISP_HOR_RES * MY_DISP_VER_RES * sizeof(lv_color_t);
    buf_3_1 = heap_caps_malloc(buffer_size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    buf_3_2 = heap_caps_malloc(buffer_size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);

    if(buf_3_1 != NULL && buf_3_2 != NULL) {
        /* 成功分配全屏缓冲区 */
        lv_disp_draw_buf_init(&draw_buf_dsc_3, buf_3_1, buf_3_2, MY_DISP_HOR_RES * MY_DISP_VER_RES);
    } else {
        /* 回退到较小的缓冲区 */
        static lv_color_t buf_fallback_1[MY_DISP_HOR_RES * 30];
        static lv_color_t buf_fallback_2[MY_DISP_HOR_RES * 30];
        lv_disp_draw_buf_init(&draw_buf_dsc_3, buf_fallback_1, buf_fallback_2, MY_DISP_HOR_RES * 30);
    }

    /*-----------------------------------
     * Register the display in LVGL
     *----------------------------------*/

    static lv_disp_drv_t disp_drv;                         /*Descriptor of a display driver*/
    lv_disp_drv_init(&disp_drv);                    /*Basic initialization*/

    /*Set up the functions to access to your display*/

    /*Set the resolution of the display*/
    disp_drv.hor_res = MY_DISP_HOR_RES;
    disp_drv.ver_res = MY_DISP_VER_RES;

    /*Used to copy the buffer's content to the display*/
    disp_drv.flush_cb = disp_flush;

    /*Set a display buffer - 使用全局双缓冲区*/
    disp_drv.draw_buf = &draw_buf_dsc_3;

    /*启用全屏刷新模式，配合全局双缓冲获得最佳性能*/
    disp_drv.full_refresh = 1;  /* 全屏刷新模式，避免部分刷新问题 */

    /* Fill a memory array with a color if you have GPU.
     * Note that, in lv_conf.h you can enable GPUs that has built-in support in LVGL.
     * But if you have a different GPU you can use with this callback.*/
    //disp_drv.gpu_fill_cb = gpu_fill;

    /*Finally register the driver*/
    lv_disp_drv_register(&disp_drv);
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/*Initialize your display and the required peripherals.*/
static void disp_init(void)
{
    
    ltdc_init();
}

volatile bool disp_flush_enabled = true;

/* Enable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_enable_update(void)
{
    disp_flush_enabled = true;
}

/* Disable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_disable_update(void)
{
    disp_flush_enabled = false;
}

/* Force refresh the entire screen to fix display issues
 */
void disp_force_refresh(void)
{
    lv_obj_invalidate(lv_scr_act());  /* 强制刷新当前屏幕 */
}

/*Flush the content of the internal buffer the specific area on the display
 *全局双缓冲+DMA模式，提供最佳性能和显示质量*/
static void disp_flush(lv_disp_drv_t * disp_drv, const lv_area_t * area, lv_color_t * color_p)
{
    if(disp_flush_enabled) {
        /* 全屏刷新模式：直接使用ESP32 LCD面板的DMA传输 */
        /* 在全屏刷新模式下，area总是整个屏幕，color_p包含完整的帧缓冲 */
        extern esp_lcd_panel_handle_t panel_handle;

        /* 使用ESP32 LCD面板API进行高速DMA传输 */
        /* 这是最高效的传输方式，直接将整个帧缓冲传输到显示器 */
        esp_lcd_panel_draw_bitmap(panel_handle, 0, 0, MY_DISP_HOR_RES, MY_DISP_VER_RES, (uint16_t*)color_p);
    }

    /*IMPORTANT!!!
     *通知图形库刷新完成，全局双缓冲机制会自动切换缓冲区*/
    lv_disp_flush_ready(disp_drv);
}

/*OPTIONAL: GPU INTERFACE*/

/*If your MCU has hardware accelerator (GPU) then you can use it to fill a memory with a color*/
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//                    const lv_area_t * fill_area, lv_color_t color)
//{
//    /*It's an example code which should be done by your GPU*/
//    int32_t x, y;
//    dest_buf += dest_width * fill_area->y1; /*Go to the first line*/
//
//    for(y = fill_area->y1; y <= fill_area->y2; y++) {
//        for(x = fill_area->x1; x <= fill_area->x2; x++) {
//            dest_buf[x] = color;
//        }
//        dest_buf+=dest_width;    /*Go to the next line*/
//    }
//}


#else /*Enable this file at the top*/

/*This dummy typedef exists purely to silence -Wpedantic.*/
typedef int keep_pedantic_happy;
#endif
