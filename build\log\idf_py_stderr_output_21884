CMake Error: The current CMakeCache.txt directory C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/build/CMakeCache.txt is different than the directory c:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt
CMake Error: The source "C:/Users/<USER>/Desktop/ESP32/Code/5.Location/project/CMakeLists.txt" does not match the source "C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/CMakeLists.txt" used to generate cache.  Re-run cmake with a different source directory.
